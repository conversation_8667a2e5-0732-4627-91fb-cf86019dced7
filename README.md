### README

#### ODS to Pandas DataFrame (ods2df.py)

This project provides a lightweight utility to read a sheet from an OpenDocument Spreadsheet (.ods) file—whether unprotected or password-protected-and convert it into a Pandas DataFrame.

It parses ODF XML directly from the .ods ZIP, with optional decryption for password-protected files using parameters discovered in META-INF/manifest.xml. It handles repeated rows/columns, trims trailing empty rows, and attempts to preserve basic value types (floats and strings).

#### Features

- Reads a specific sheet by name from .ods files.
- Supports password-protected ODS files (AES-256-CBC and Blowfish-CFB variants commonly seen in older ODF files).
- Derives keys via PBKDF2 (with support for start-key-generation hints found in manifest).
- Decompresses and parses content.xml robustly with multiple zlib fallbacks.
- Handles ODF table repeat semantics: number-rows-repeated and number-columns-repeated.
- Trims trailing empty rows for a cleaner DataFrame.
- Returns a Pandas DataFrame you can use immediately.

#### Requirements

- Python 3.11+
- pandas
- cryptography

#### Usage

Basic example:

```python
from ods2df import read_ods_sheet_to_dataframe

file_path = "path/to/workbook.ods"
sheet_name = "Sheet1"

df = read_ods_sheet_to_dataframe(file_path, sheet_name)
print(df.head())
```

Password-protected file:

```python
from ods2df import read_ods_sheet_to_dataframe

file_path = "path/to/protected.ods"
sheet_name = "Data"
password = "YourPassword"

df = read_ods_sheet_to_dataframe(file_path, sheet_name, password=password)
print(df.shape)
```

#### Function Reference

read_ods_sheet_to_dataframe(file_path, sheet_name, password=None)

- file_path: path to the .ods file.
- sheet_name: name of the sheet (table:table[@table:name]) to load.
- password: optional password for encrypted files. If the file is protected and no password is provided, a ValueError is raised.

Returns: pandas.DataFrame with the sheet’s data. Empty trailing rows are removed. Cells are currently parsed as float, string, or None; other ODF types (dates, booleans, currency) can be added as needed.

#### Notes on Encryption Support

- The function inspects META-INF/manifest.xml for encryption-data and derives decryption parameters accordingly.
- AES-256-CBC: PKCS#7 padding is removed; a 1K checksum may be verified against uncompressed content.
- Blowfish-CFB: processed without PKCS#7 padding; checksum verification is attempted with best-effort fallback and may be skipped with a warning due to legacy variations.
- PBKDF2 derivation uses SHA-1 by default for compatibility with common ODF implementations; start-key-generation hints (e.g., SHA256) are honored to pre-hash the password before PBKDF2.

If decryption or decompression fails, the function tries multiple zlib window configurations and provides actionable error messages (wrong password, corruption, or unsupported format).

#### Limitations and Future Improvements

- Limited type inference: only floats and strings are explicitly handled; date, boolean, and other ODF value types are returned as None and can be extended.
- No merged cell handling: repeated columns/rows are expanded but merged-cell semantics are not interpreted.
- Only a subset of ODF encryption variants is supported; highly customized or uncommon encryption schemes may not work.
- Logging currently uses print statements for debug; consider switching to Python’s logging for production use.

#### Troubleshooting

- ValueError: “File is password-protected but no password provided.” Provide the correct password.
- ValueError: “Checksum mismatch.” Confirm the password and file integrity; some legacy files may have differing checksum behavior.
- “All decompression attempts failed.” File may be corrupted or use an unsupported compression or encryption variant.
- “Sheet 'X' not found.” Verify the exact sheet name, including case and spaces. The script logs discovered sheet names when parsing.

#### License

Apache-2.0

#### Acknowledgments

- Uses Python’s standard library (zipfile, zlib, xml.etree.ElementTree) for ZIP and XML handling.
- Uses cryptography for decryption (AES, Blowfish, PBKDF2, hashes).
- Built for direct, dependency-light access to ODS content when avoiding heavier ODF libraries.
