import zipfile
import zlib
from xml.etree import ElementTree as ET
import base64
import pandas as pd
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.decrepit.ciphers.algorithms import Blowfish
from cryptography.hazmat.backends import default_backend


def read_ods_sheet_to_dataframe(file_path, sheet_name, password=None):
    backend = default_backend()

    with zipfile.ZipFile(file_path, 'r') as zf:
        # Extract manifest.xml (not encrypted)
        with zf.open('META-INF/manifest.xml') as manifest_file:
            manifest_tree = ET.parse(manifest_file)
            manifest_root = manifest_tree.getroot()

        # Find encryption params for content.xml
        ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}
        entry = manifest_root.find(".//manifest:file-entry[@manifest:full-path='content.xml']", ns)
        if entry is None:
            raise ValueError("content.xml not found in manifest.")

        enc_data = entry.find('manifest:encryption-data', ns)

        if enc_data is not None:
            # File is password-protected
            if password is None:
                raise ValueError("File is password-protected but no password provided.")

            # Proceed with decryption (original logic)
            alg = enc_data.find('manifest:algorithm', ns)
            key_deriv = enc_data.find('manifest:key-derivation', ns)
            start_key_gen = enc_data.find('manifest:start-key-generation', ns)  # Optional in some versions

            # Parse params using qualified attribute names
            algorithm_name = alg.get(f"{{{ns['manifest']}}}algorithm-name")
            print(f"Detected algorithm: {algorithm_name}")  # For debugging

            iv = base64.b64decode(alg.get(f"{{{ns['manifest']}}}initialisation-vector"))
            salt = base64.b64decode(key_deriv.get(f"{{{ns['manifest']}}}salt"))
            iteration_count = int(key_deriv.get(f"{{{ns['manifest']}}}iteration-count"))
            key_size = int(key_deriv.get(f"{{{ns['manifest']}}}key-size", 32))  # 256-bit
            checksum_type = enc_data.get(f"{{{ns['manifest']}}}checksum-type", 'SHA256/1K')  # Common default
            checksum = base64.b64decode(enc_data.get(f"{{{ns['manifest']}}}checksum"))

            if 'aes256-cbc' in algorithm_name.lower():
                cipher_algorithm = algorithms.AES
                mode = modes.CBC(iv)
                use_pkcs7_padding = True  # For AES
            elif 'blowfish' in algorithm_name.lower() and 'cfb' in algorithm_name.lower():
                if len(iv) != 8:
                    raise ValueError("Invalid IV length for Blowfish CFB.")
                cipher_algorithm = Blowfish
                mode = modes.CFB(iv)
                if key_size != 16:  # Adjust to common Blowfish key size
                    key_size = 16
                use_pkcs7_padding = False  # Try without padding for Blowfish CFB
            else:
                raise ValueError(f"Unsupported algorithm: {algorithm_name}.")

            # Determine hash algorithm for start-key and checksum
            start_hash_alg = hashes.SHA256()
            check_hash_alg = hashes.SHA256()
            if start_key_gen is not None:
                start_key_name = start_key_gen.get(f"{{{ns['manifest']}}}start-key-generation-name", 'SHA256')
                print(f"Detected start-key-generation: {start_key_name}")
                if 'SHA1' in start_key_name.upper():
                    start_hash_alg = hashes.SHA1()
            if 'SHA1' in checksum_type.upper():
                check_hash_alg = hashes.SHA1()

            # For PBKDF2, force SHA1 (common in some ODF impls, even if start is SHA256)
            kdf_hash_alg = hashes.SHA1()  # Test this; change to hashes.SHA256() if it fails

            # Derive key from password (UTF-8 encoded)
            password_bytes = password.encode('utf-8')

            # If start-key-generation is present, pre-hash password to get start key
            if start_key_gen is not None:
                start_hash = hashes.Hash(start_hash_alg, backend=backend)
                start_hash.update(password_bytes)
                password_bytes = start_hash.finalize()  # Use this as input to PBKDF2

            kdf = PBKDF2HMAC(
                algorithm=kdf_hash_alg,
                length=key_size,
                salt=salt,
                iterations=iteration_count,
                backend=backend
            )
            key = kdf.derive(password_bytes)

            # Extract encrypted content.xml bytes
            with zf.open('content.xml') as encrypted_file:
                encrypted_data = encrypted_file.read()

            # Decrypt
            cipher = Cipher(cipher_algorithm(key), mode, backend=backend)
            decryptor = cipher.decryptor()
            decrypted_data = decryptor.update(
                encrypted_data) + decryptor.finalize()  # Raw decrypted (possibly compressed)

            # Conditional padding removal
            if use_pkcs7_padding:
                # Remove PKCS7 padding (ODF uses PKCS7)
                padding_len = decrypted_data[-1]
                decrypted_compressed = decrypted_data[:-padding_len]
            else:
                decrypted_compressed = decrypted_data  # No padding for Blowfish

            print(f"First 20 decrypted_compressed bytes: {decrypted_compressed[:20]}")  # Debug

            # Enhanced decompression with more fallbacks
            window_bits_options = [-zlib.MAX_WBITS, -8, 15, 0]  # Try raw DEFLATE, minimal, with header, auto
            decrypted_xml = None
            for wb in window_bits_options:
                try:
                    decrypted_xml = zlib.decompress(decrypted_compressed, wb)
                    print(f"Successful decompression with window bits: {wb}")
                    print(f"First 20 decrypted bytes: {decrypted_xml[:20]}")
                    break
                except zlib.error as e:
                    print(f"Decompression failed with {wb}: {e}")
            if decrypted_xml is None:
                raise ValueError(
                    "All decompression attempts failed. Possible wrong password, corruption, or format mismatch.")

            # Verify checksum (of first 1024 bytes of uncompressed data, or as per checksum-type)
            if 'aes' in algorithm_name.lower() and '1K' in checksum_type:  # Only enforce for AES
                check_hash = hashes.Hash(check_hash_alg, backend=backend)
                check_hash.update(decrypted_xml[:1024])
                computed_checksum = check_hash.finalize()
                if computed_checksum != checksum:
                    raise ValueError("Checksum mismatch. Wrong password or corrupted data.")
            # For Blowfish, optionally try alternative checksum (e.g., of compressed data)
            elif 'blowfish' in algorithm_name.lower() and '1K' in checksum_type:
                try:
                    check_hash = hashes.Hash(check_hash_alg, backend=backend)
                    check_hash.update(decrypted_compressed[:1024])  # Try compressed instead
                    computed_checksum = check_hash.finalize()
                    if computed_checksum != checksum:
                        print("Warning: Checksum mismatch on compressed data; proceeding anyway for legacy file.")
                except:
                    print("Warning: Checksum verification skipped for Blowfish.")

        else:
            # File is not password-protected; read content.xml directly (zipfile handles any compression)
            if password is not None:
                print("Warning: Password provided but file is not protected. Ignoring password.")
            with zf.open('content.xml') as f:
                decrypted_xml = f.read()

        # Parse the decrypted XML
        tree = ET.fromstring(decrypted_xml)

    # Define namespaces for parsing
    ns = {
        'table': 'urn:oasis:names:tc:opendocument:xmlns:table:1.0',
        'office': 'urn:oasis:names:tc:opendocument:xmlns:office:1.0',
        'text': 'urn:oasis:names:tc:opendocument:xmlns:text:1.0',
    }

    # Find the spreadsheet body
    spreadsheet = tree.find('.//office:body/office:spreadsheet', ns)
    if spreadsheet is None:
        raise ValueError("No spreadsheet found in the document.")

    # Find the specific sheet (using qualified attribute for table:name)
    sheet = None
    for s in spreadsheet.findall('table:table', ns):
        # Use qualified name for the attribute
        current_name = s.get(f"{{{ns['table']}}}name")
        print(f"Found sheet: {current_name}")  # Debug: Print actual sheet names
        if current_name == sheet_name:
            sheet = s
            break
    if sheet is None:
        raise ValueError(f"Sheet '{sheet_name}' not found.")

    # Extract data, handling repeats and basic value types
    data = []
    max_cols = 0
    for row_elem in sheet.findall('table:table-row', ns):
        row_repeat = int(row_elem.get(f"{{{ns['table']}}}number-rows-repeated", '1'))
        row_data = []

        # Collect cell elements to identify the last one
        cell_elems = list(row_elem.iterfind('table:table-cell', ns))
        for i, cell_elem in enumerate(cell_elems):
            cell_repeat = int(cell_elem.get(f"{{{ns['table']}}}number-columns-repeated", '1'))
            value_type = cell_elem.get('office:value-type')
            formula = cell_elem.get(f"{{{ns['table']}}}formula")  # Check for formula attribute
            p = cell_elem.find('text:p', ns)

            if formula:
                value = formula  # Store the formula string as is
            elif value_type == 'float':
                value = float(cell_elem.get('office:value', '0'))
            elif value_type == 'percentage':
                value = float(cell_elem.get('office:value', '0'))  # Treat as float (e.g., 0.25 for 25%)
            elif value_type == 'currency':
                float_value = float(cell_elem.get('office:value', '0'))
                currency_symbol = cell_elem.get('office:currency', '')  # e.g., 'USD'
                value = f"{currency_symbol} {float_value}"  # String like 'USD 123.45';
            elif value_type == 'date':
                value = cell_elem.get('office:date-value')  # ISO date string
            elif value_type == 'time':
                value = cell_elem.get('office:time-value')  # Duration or time string
            elif value_type == 'boolean':
                bool_str = cell_elem.get('office:boolean-value')
                value = True if bool_str == 'true' else False if bool_str == 'false' else None
            elif value_type == 'string' or (value_type is None and p is not None):
                value = p.text if p is not None else ''
            else:
                value = None  # For images, objects, void, or unsupported types, treat as empty

            # Skip extending if this is the last cell and it's empty (trailing empty repeat)
            if i == len(cell_elems) - 1 and value is None:
                continue

            row_data.extend([value] * cell_repeat)

        # Update max_cols based on effective length (without trailing empties)
        max_cols = max(max_cols, len(row_data))

        # Extend data with the repeated rows
        data.extend([row_data] * row_repeat)

    # Pad all rows to max_cols with None
    for i in range(len(data)):
        data[i] += [None] * (max_cols - len(data[i]))

    print(f"Rows before trimming: {len(data)}")  # Debug

    # Trim trailing empty rows (all None) - only continuous from the end
    while data and all(v is None for v in data[-1]):
        data.pop()

    print(f"Rows after trimming: {len(data)}")  # Debug

    # Create DataFrame
    df = pd.DataFrame(data)
    return df
