[project]
name = "ods2df"
version = "0.1.0"
description = "Lightweight utility to read a sheet from an .ods file—whether unprotected or password-protected-and convert it into a Pandas DataFrame."
authors = [
    {name = "Art",email = "<EMAIL>"}
]
license = "Apache-2.0"
packages = [{include = "ods2df", from = "src"}]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "pandas (>=2.3.1,<3.0.0)",
    "click (>=8.2.1,<9.0.0)",
    "cryptography (>=45.0.6,<46.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"


[tool.pytest.ini_options]
testpaths = "tests"

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
ipython = "^9.4.0"

