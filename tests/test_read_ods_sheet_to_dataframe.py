"""
Unit tests for read_ods_sheet_to_dataframe function
"""
import pytest
import pandas as pd
import os
from pathlib import Path
from src.ods2df.ods2df import read_ods_sheet_to_dataframe


class TestReadOdsSheetToDataframe:
    """Test class for read_ods_sheet_to_dataframe function"""
    
    @pytest.fixture
    def test_data_dir(self):
        """Fixture to get the test data directory path"""
        return Path(__file__).parent / "resource"
    
    @pytest.fixture
    def expected_data(self):
        """Fixture to load expected data from CSV file"""
        csv_path = Path(__file__).parent / "resource" / "file_example_ODS_10.csv"
        return pd.read_csv(csv_path)
    
    @pytest.fixture
    def sheet_name(self):
        """Fixture for the sheet name"""
        return "Sheet1"
    
    @pytest.fixture
    def password(self):
        """Fixture for the password used in protected files"""
        return "Overload_Idealize_Scale"
    
    def test_read_unprotected_ods_file(self, test_data_dir, expected_data, sheet_name):
        """Test reading an unprotected ODS file"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        
        # Read the ODS file
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)
        
        # Verify the DataFrame is not empty
        assert not df.empty, "DataFrame should not be empty"
        
        # Verify the shape matches expected data
        assert df.shape == expected_data.shape, f"Expected shape {expected_data.shape}, got {df.shape}"
        
        # Verify the data content matches (convert to string for comparison since ODS may have different types)
        for i in range(len(expected_data)):
            for j in range(len(expected_data.columns)):
                expected_val = str(expected_data.iloc[i, j])
                actual_val = str(df.iloc[i, j])
                assert expected_val == actual_val, f"Mismatch at row {i}, col {j}: expected {expected_val}, got {actual_val}"
    
    def test_read_protected_ods_file_aes256(self, test_data_dir, expected_data, sheet_name, password):
        """Test reading a password-protected ODS file with AES-256-CBC encryption"""
        file_path = test_data_dir / "file_example_ODS_10.protected.1.ods"
        
        # Read the protected ODS file with correct password
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name, password=password)
        
        # Verify the DataFrame is not empty
        assert not df.empty, "DataFrame should not be empty"
        
        # Verify the shape matches expected data
        assert df.shape == expected_data.shape, f"Expected shape {expected_data.shape}, got {df.shape}"
        
        # Verify the data content matches
        for i in range(len(expected_data)):
            for j in range(len(expected_data.columns)):
                expected_val = str(expected_data.iloc[i, j])
                actual_val = str(df.iloc[i, j])
                assert expected_val == actual_val, f"Mismatch at row {i}, col {j}: expected {expected_val}, got {actual_val}"
    
    def test_read_protected_ods_file_blowfish(self, test_data_dir, expected_data, sheet_name, password):
        """Test reading a password-protected ODS file with Blowfish CFB encryption"""
        file_path = test_data_dir / "file_example_ODS_10.protected.2.ods"
        
        # Read the protected ODS file with correct password
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name, password=password)
        
        # Verify the DataFrame is not empty
        assert not df.empty, "DataFrame should not be empty"
        
        # Verify the shape matches expected data
        assert df.shape == expected_data.shape, f"Expected shape {expected_data.shape}, got {df.shape}"
        
        # Verify the data content matches
        for i in range(len(expected_data)):
            for j in range(len(expected_data.columns)):
                expected_val = str(expected_data.iloc[i, j])
                actual_val = str(df.iloc[i, j])
                assert expected_val == actual_val, f"Mismatch at row {i}, col {j}: expected {expected_val}, got {actual_val}"
    
    def test_protected_file_without_password(self, test_data_dir, sheet_name):
        """Test that reading a protected file without password raises ValueError"""
        file_path = test_data_dir / "file_example_ODS_10.protected.1.ods"
        
        with pytest.raises(ValueError, match="File is password-protected but no password provided"):
            read_ods_sheet_to_dataframe(str(file_path), sheet_name)
    
    def test_protected_file_with_wrong_password(self, test_data_dir, sheet_name):
        """Test that reading a protected file with wrong password raises an error"""
        file_path = test_data_dir / "file_example_ODS_10.protected.1.ods"
        wrong_password = "wrong_password"
        
        with pytest.raises((ValueError, Exception)):
            read_ods_sheet_to_dataframe(str(file_path), sheet_name, password=wrong_password)
    
    def test_nonexistent_sheet_name(self, test_data_dir):
        """Test that requesting a non-existent sheet raises ValueError"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        nonexistent_sheet = "NonExistentSheet"
        
        with pytest.raises(ValueError, match=f"Sheet '{nonexistent_sheet}' not found"):
            read_ods_sheet_to_dataframe(str(file_path), nonexistent_sheet)
    
    def test_nonexistent_file(self, sheet_name):
        """Test that reading a non-existent file raises appropriate error"""
        nonexistent_file = "nonexistent_file.ods"
        
        with pytest.raises((FileNotFoundError, Exception)):
            read_ods_sheet_to_dataframe(nonexistent_file, sheet_name)
    
    def test_unprotected_file_with_password_warning(self, test_data_dir, expected_data, sheet_name, password, capsys):
        """Test that providing password for unprotected file shows warning but still works"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        
        # Read unprotected file with password (should show warning)
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name, password=password)
        
        # Verify the DataFrame is correct despite the warning
        assert not df.empty, "DataFrame should not be empty"
        assert df.shape == expected_data.shape, f"Expected shape {expected_data.shape}, got {df.shape}"
        
        # Check that warning was printed
        captured = capsys.readouterr()
        assert "Warning: Password provided but file is not protected" in captured.out
    
    def test_dataframe_structure(self, test_data_dir, sheet_name):
        """Test that the returned object is a proper pandas DataFrame"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)
        
        # Verify it's a DataFrame
        assert isinstance(df, pd.DataFrame), "Return value should be a pandas DataFrame"
        
        # Verify it has the expected number of rows and columns
        assert df.shape[0] == 10, f"Expected 10 rows, got {df.shape[0]}"
        assert df.shape[1] == 8, f"Expected 8 columns, got {df.shape[1]}"
    
    def test_data_types_and_content(self, test_data_dir, sheet_name):
        """Test that data types and content are correctly parsed"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)
        
        # Check first row (header row)
        expected_headers = ["0", "First Name", "Last Name", "Gender", "Country", "Age", "Date", "Id"]
        for i, expected_header in enumerate(expected_headers):
            assert str(df.iloc[0, i]) == expected_header, f"Header mismatch at column {i}"
        
        # Check a specific data row (row 1, which is the first data row)
        assert str(df.iloc[1, 0]) == "1", "First column should be '1'"
        assert str(df.iloc[1, 1]) == "Dulce", "Second column should be 'Dulce'"
        assert str(df.iloc[1, 2]) == "Abril", "Third column should be 'Abril'"
        assert str(df.iloc[1, 3]) == "Female", "Fourth column should be 'Female'"
    
    def test_all_files_produce_same_data(self, test_data_dir, expected_data, sheet_name, password):
        """Test that all ODS files (protected and unprotected) produce the same data"""
        files_to_test = [
            ("file_example_ODS_10.ods", None),
            ("file_example_ODS_10.protected.1.ods", password),
            ("file_example_ODS_10.protected.2.ods", password)
        ]
        
        dataframes = []
        
        for filename, file_password in files_to_test:
            file_path = test_data_dir / filename
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name, password=file_password)
            dataframes.append(df)
        
        # Compare all dataframes to ensure they're identical
        base_df = dataframes[0]
        for i, df in enumerate(dataframes[1:], 1):
            assert df.shape == base_df.shape, f"DataFrame {i} shape mismatch"
            
            # Compare content
            for row in range(len(df)):
                for col in range(len(df.columns)):
                    base_val = str(base_df.iloc[row, col])
                    current_val = str(df.iloc[row, col])
                    assert base_val == current_val, f"Data mismatch in file {i} at row {row}, col {col}"
